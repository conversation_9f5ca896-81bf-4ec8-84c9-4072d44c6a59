import json
import requests
from bs4 import BeautifulSoup
import re
import argparse
from typing import List, Dict, Any, Optional
from rich.progress import Progress, TaskID, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn, TimeRemainingColumn
from rich.console import Console

from job_seeker_cli.services.path_manager import Path<PERSON>anager
from job_seeker_cli.services.file_service import FileService

# Common headers to mimic browser
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) '
                  'AppleWebKit/537.36 (KHTML, like Gecko) '
                  'Chrome/135.0.0.0 Safari/537.36',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Referer': 'https://www.seek.com.au',
    'Origin': 'https://www.seek.com.au'
}

def clean_unicode_text(text: str) -> str:
    """Clean text of invalid unicode characters."""
    if not isinstance(text, str):
        return text
    return re.sub(r'[\ud800-\udfff]', '', text)

class JobFetcher:
    def __init__(self, file_service: FileService):
        self.file_service = file_service
        self.console = Console()

    def fetch_job_details(self, input_filename: str, output_filename: str):
        """
        Fetches job details for a list of jobs from a JSON file.

        Args:
            input_filename (str): The name of the input JSON file in the 'input' directory.
            output_filename (str): The name for the output JSON file in the 'processed' directory.
        """
        jobs: Optional[List[Dict[str, Any]]] = self.file_service.read_json("input", input_filename)
        if not jobs:
            self.console.print("[bold red]Could not read or parse [/bold red]" + input_filename)
            return

        # 创建一个美观的进度条
        with Progress(
            SpinnerColumn(),
            TextColumn("[bold blue]{task.description}[/bold blue]"),
            BarColumn(bar_width=40),
            TextColumn("[bold green]{task.completed}/{task.total}[/bold green]"),
            TimeElapsedColumn(),
            TimeRemainingColumn(),
            console=self.console
        ) as progress:
            # 创建主任务
            main_task = progress.add_task("[cyan]抓取职位详情[/cyan]", total=len(jobs))
            
            for i, job in enumerate(jobs):
                job_title = job.get('jobTitle', 'N/A')
                # 更新任务描述
                progress.update(main_task, description=f"[cyan]抓取职位详情: {job_title}[/cyan]")
                
                url = job.get('recommendedJobLink')
                if not url:
                    progress.log(f"[yellow]职位 {i+1} 缺少链接。跳过。[/yellow]")
                    job['jobDetails'] = ''
                    progress.update(main_task, advance=1)
                    continue

                # 创建子任务来显示当前正在处理的职位
                job_task = progress.add_task(f"[magenta]处理: {job_title}[/magenta]", total=3)
                
                # 步骤1: 获取网页
                progress.update(job_task, description=f"[magenta]下载: {job_title}[/magenta]")
                try:
                    resp = requests.get(url, headers=HEADERS, timeout=10)
                    resp.raise_for_status()
                    progress.update(job_task, advance=1)
                except requests.RequestException as e:
                    progress.log(f"[red]无法获取 {url}: {e}[/red]")
                    job['jobDetails'] = ''
                    progress.update(main_task, advance=1)
                    progress.remove_task(job_task)
                    continue

                # 步骤2: 解析内容
                progress.update(job_task, description=f"[magenta]解析: {job_title}[/magenta]")
                soup = BeautifulSoup(resp.text, 'html.parser')
                detail_div = soup.find(attrs={'data-automation': 'jobAdDetails'})
                
                details_text = ''
                if detail_div:
                    details_text = detail_div.get_text(separator='\\n').strip()
                else:
                    ld_script = soup.find('script', type='application/ld+json')
                    if ld_script:
                        try:
                            ld = json.loads(ld_script.string)
                            details_text = ld.get('description', '').strip()
                        except json.JSONDecodeError:
                            progress.log(f"[yellow]无法解析 JSON-LD: {url}[/yellow]")
                    else:
                        progress.log(f"[yellow]未找到详情部分: {url}[/yellow]")
                progress.update(job_task, advance=1)

                # 步骤3: 清理数据
                progress.update(job_task, description=f"[magenta]清理: {job_title}[/magenta]")
                job['jobDetails'] = clean_unicode_text(details_text)
                for field in ['jobTitle', 'jobAdvertiser']:
                    if field in job and isinstance(job[field], str):
                        job[field] = clean_unicode_text(job[field])
                progress.update(job_task, advance=1)
                
                # 完成这个职位的处理
                progress.remove_task(job_task)
                progress.update(main_task, advance=1)

        # 保存结果
        self.file_service.write_json("processed", output_filename, jobs)
        self.console.print(f"[bold green]成功获取并更新了 {len(jobs)} 个职位的详情。[/bold green]")
        self.console.print(f"[bold blue]结果已保存到 'processed/{output_filename}'[/bold blue]")

def main():
    parser = argparse.ArgumentParser(description="Fetch job details from a job list file.")
    parser.add_argument('--input', type=str, required=True, help='Input job list JSON file name (e.g., joblist_0619.json)')
    parser.add_argument('--output', type=str, required=True, help='Output file name for processed jobs (e.g., joblist_0619_details.json)')
    args = parser.parse_args()

    path_manager = PathManager()
    file_service = FileService(path_manager)
    fetcher = JobFetcher(file_service)
    
    fetcher.fetch_job_details(args.input, args.output)

if __name__ == "__main__":
    main()